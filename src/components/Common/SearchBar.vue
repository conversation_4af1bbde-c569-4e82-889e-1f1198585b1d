<template>
  <div class="search-container">
    <div class="search-input-wrapper">
      <div class="input-container">
        <!-- 麦克风按钮在输入框内部左侧 -->
        <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
          <i class="iconfont icon-microphone" class-prefix="icon"></i>
        </div>
        <input
          v-model="searchQuery"
          type="text"
          class="search-input"
          :placeholder="isRecording ? '我在听，请说...' : placeholder"
          maxlength="50"
          @input="onSearchInput"
        />
        <button v-show="searchQuery.trim()" class="clear-btn" @click="handleClear">×</button>
      </div>
      <button class="add-person-btn-header" @click="handleAdd">
        <span class="add-icon">+</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount } from 'vue';
import { showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';

// Props
interface IProps {
  placeholder?: string;
}

withDefaults(defineProps<IProps>(), {
  placeholder: '请输入姓名进行搜索',
});

// Emits
const emit = defineEmits<{
  search: [query: string];
  add: [];
  input: [query: string];
}>();

// 响应式数据
const searchQuery = ref('');
let searchTimer: NodeJS.Timeout | null = null;

// 语音录制相关状态
const isRecording = ref(false);
const micPermission = ref(false);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let mediaStream: MediaStream | null = null;
let timerId: NodeJS.Timeout | null = null;
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref<ArrayBuffer | null>(null);
const voiceMessage = ref('');
const lastVoiceText = ref('');

// 处理搜索
const handleSearch = (query: string) => {
  if (query.trim()) {
    emit('search', query.trim());
  }
};

// 处理添加
const handleAdd = () => {
  emit('add');
};

// 处理清空
const handleClear = () => {
  searchQuery.value = '';
  // 清除搜索定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
  // 通知父组件输入变化
  emit('input', '');
};

// 处理输入 - 实现防抖搜索
const onSearchInput = () => {
  emit('input', searchQuery.value);

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，2秒后执行搜索
  searchTimer = setTimeout(() => {
    handleSearch(searchQuery.value);
  }, 1000);
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限（仅在需要时请求）
async function setMicPermission() {
  try {
    // 保存媒体流引用，用于后续释放资源
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    // 如果浏览器不支持录音功能，给用户提示
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  // 当录音开始时的回调
  recorder.onstart = () => {};

  // 处理录音错误的回调
  recorder.onstreamerror = () => {
    // 显示录音错误消息并停止录音
    showToast('录音失败');
    cancelRecording();
  };

  // 处理录音数据的回调
  recorder.ondataavailable = debounce(async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      try {
        const streamData = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: data.buffer,
        });

        // 检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
        if (
          streamData.data.full_text &&
          streamData.data.full_text.trim() !== '' &&
          streamData.data.full_text !== lastVoiceText.value
        ) {
          const newText = streamData.data.full_text;
          lastVoiceText.value = newText;
          voiceMessage.value = newText;
          // 将语音识别的文字设置到搜索框
          searchQuery.value = newText;
          // 触发输入事件
          emit('input', searchQuery.value);
        }
      } catch (error) {
        console.error('语音识别失败:', error);
      }
    }
  }, 300);
};

// 取消录音
const cancelRecording = () => {
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  // 释放麦克风资源
  releaseMicrophoneResources();
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (voiceMessage.value) {
    console.log('📤 [SearchBar] 语音识别完成:', voiceMessage.value);
    // 触发搜索
    handleSearch(voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 处理语音按钮点击 - 直接开始录音
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (timerId) {
    clearTimeout(timerId);
  }
  if (searchTimer) {
    clearTimeout(searchTimer);
  }
  releaseMicrophoneResources();
});

// 暴露方法供父组件调用
defineExpose({
  clearSearch: () => {
    searchQuery.value = '';
    // 清除搜索定时器
    if (searchTimer) {
      clearTimeout(searchTimer);
      searchTimer = null;
    }
  },
  getSearchQuery: () => searchQuery.value,
});
</script>

<style lang="scss" scoped>
// 统一配色方案
:root {
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);

  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-disabled: rgba(255, 255, 255, 0.5);

  --bg-glass: rgba(30, 58, 138, 0.15);
  --bg-glass-hover: rgba(30, 58, 138, 0.25);
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(0, 255, 255, 0.3);

  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
  --shadow-accent: 0 0 20px rgba(0, 255, 255, 0.2);

  --border-radius-xl: 20px;
}

.search-container {
  padding: 18px 0px;

  .search-input-wrapper {
    display: flex;
    gap: 20px;
    align-items: center;

    .input-container {
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;

      .voice-toggle-inner {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        z-index: 10;

        &.breathing {
          animation: breathing 2s ease-in-out infinite;
        }

        .iconfont {
          font-size: 28px;
          color: var(--text-primary);
        }
      }

      .search-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 18px;
        padding: 16px 20px;
        padding-left: 90px; // 为语音按钮留出更多空间，与inputBar保持一致
        padding-right: 50px; // 为清空按钮留出空间
        color: rgba(255, 255, 255, 0.95);
        font-size: 26px; // 增大字体，原来是22px
        box-sizing: border-box;
        transition: all 0.2s ease;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          outline: none;
          border-color: rgba(255, 255, 255, 0.5);
          background: rgba(255, 255, 255, 0.15);
        }
      }

      .clear-btn {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }

    .add-person-btn-header {
      padding: 16px 16px;
      border-radius: 50%;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      border: 2px solid;
      color: #00bcd4;
      border-color: #00bcd4;
      background: rgba(0, 188, 212, 0.15);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      min-width: 60px;
      height: 58px; // 与search-input高度一致
      box-sizing: border-box;

      .add-icon {
        font-size: 32px;
        line-height: 1;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

@keyframes breathing {
  0% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.6);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 0 8px rgba(0, 255, 255, 0.2);
  }
  100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.6);
  }
}
</style>
